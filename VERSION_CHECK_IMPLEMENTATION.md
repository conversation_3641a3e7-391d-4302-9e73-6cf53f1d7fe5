# 📋 版本检查功能实现文档

## 🎯 功能概述

本文档描述了HOUT工具箱的版本检查功能实现，该功能在软件启动时自动检查版本更新，并根据版本比较结果提供相应的用户提示。

## 🏗️ 架构设计

### 核心组件

1. **VersionService** - 版本检查服务
2. **VersionChecker** - 版本检查UI组件
3. **VersionComparator** - 版本比较工具类
4. **API配置** - API调用配置管理

### 文件结构

```
src/
├── services/
│   └── versionService.ts          # 版本检查服务
├── components/
│   ├── Common/
│   │   └── VersionChecker.tsx     # 版本检查UI组件
│   └── Test/
│       └── VersionCheckTest.tsx   # 版本检查测试组件
├── types/
│   └── app.ts                     # 版本相关类型定义
├── config/
│   └── api.ts                     # API配置文件
└── App.tsx                        # 主应用集成
```

## 🔧 核心功能

### 1. 版本比较算法

支持语义化版本号（Semantic Versioning）比较：

```typescript
// 支持的版本格式
"1.0.0"           // 标准版本
"1.0.0-beta"      // 预发布版本
"1.0.0+build.1"   // 构建元数据
```

**比较规则：**
- 主版本号 > 次版本号 > 修订版本号
- 忽略预发布标识和构建元数据
- 支持不同长度的版本号比较

### 2. API集成

调用版本管理API获取最新版本信息：

```typescript
// API端点
GET /app/software/id/{id}/versions?limit=1&sortBy=releaseDate&sortOrder=desc&isStable=true

// 请求头
{
  "X-API-Key": "your-api-key",
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

### 3. 更新策略

- **无需更新**: 当前版本 >= 最新版本
- **可选更新**: 当前版本 < 最新版本（同主版本号）
- **强制更新**: 当前版本主版本号 < 最新版本主版本号

### 4. 用户界面

提供友好的更新提示对话框：
- 显示当前版本和最新版本
- 展示更新说明和文件大小
- 提供"立即更新"和"稍后更新"选项
- 强制更新时不允许关闭对话框

## 📝 API文档集成

根据`API_USAGE_GUIDE.md`文档实现：

### 版本管理API

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/app/software/id/{id}/versions` | 获取版本历史 |
| GET | `/app/software/id/{id}` | 获取软件详情 |

### 响应格式

```json
{
  "success": true,
  "data": {
    "software": {
      "id": 1,
      "name": "示例软件",
      "currentVersion": "1.2.0"
    },
    "versions": [
      {
        "id": 1,
        "version": "1.2.0",
        "releaseNotes": "更新内容",
        "releaseDate": "2025-01-29T12:00:00.000Z",
        "downloadLinks": {
          "official": "https://download.com/v1.2.0.zip"
        },
        "fileSize": "150MB",
        "isStable": true,
        "versionType": "release"
      }
    ]
  }
}
```

## 🚀 使用方法

### 1. 自动版本检查

在应用启动时自动执行：

```tsx
// App.tsx
<VersionChecker
  onUpdateRequired={handleUpdateRequired}
  onUpdateAvailable={handleUpdateAvailable}
  onCheckComplete={handleVersionCheckComplete}
  autoCheck={true}
  showDialog={true}
/>
```

### 2. 手动版本检查

在设置页面或菜单中提供手动检查选项：

```typescript
import { versionService } from '../services/versionService';

const checkForUpdates = async () => {
  const result = await versionService.checkForUpdates();
  // 处理检查结果
};
```

### 3. 版本比较

使用版本比较工具：

```typescript
import { VersionComparator } from '../services/versionService';

const needsUpdate = VersionComparator.isLess('1.0.0', '1.0.1'); // true
const isEqual = VersionComparator.isEqual('1.0.0', '1.0.0'); // true
```

## 🧪 测试

### 1. 单元测试

版本比较算法测试：

```typescript
const tests = [
  { v1: '1.0.0', v2: '1.0.1', expected: -1 },
  { v1: '1.0.1', v2: '1.0.0', expected: 1 },
  { v1: '1.0.0', v2: '1.0.0', expected: 0 },
];
```

### 2. 集成测试

访问测试页面：
- 开发模式: `http://localhost:1420?test=version`
- 静态测试: 打开 `test-version-check.html`

### 3. 测试功能

- ✅ 版本比较算法测试
- ✅ API调用模拟测试
- ✅ 更新逻辑验证
- ✅ UI组件功能测试

## ⚙️ 配置

### API配置

```typescript
// src/config/api.ts
export const API_CONFIG = {
  BASE_URL: 'https://api-g.lacs.cc',
  API_KEY: 'your-api-key',
  SOFTWARE_ID: 1,
  TIMEOUT: 10000,
};
```

### 版本配置

当前版本从以下位置获取：
1. `package.json` - 开发版本
2. `src-tauri/tauri.conf.json` - 发布版本

## 🔒 安全考虑

1. **API密钥管理**: 生产环境中应从环境变量获取
2. **请求超时**: 设置合理的超时时间防止无限等待
3. **错误处理**: 优雅处理网络错误和API异常
4. **数据验证**: 验证API返回数据的完整性

## 🐛 错误处理

### 常见错误类型

1. **网络错误**: 无法连接到API服务器
2. **认证错误**: API密钥无效或过期
3. **数据错误**: API返回数据格式不正确
4. **超时错误**: 请求超时

### 错误处理策略

```typescript
try {
  const result = await versionService.checkForUpdates();
  // 处理成功结果
} catch (error) {
  // 显示用户友好的错误信息
  console.error('版本检查失败:', error);
}
```

## 📈 性能优化

1. **缓存机制**: 避免频繁的API调用
2. **异步加载**: 不阻塞应用启动
3. **超时控制**: 防止长时间等待
4. **错误重试**: 网络异常时自动重试

## 🔄 更新流程

1. **启动检查**: 应用启动时自动检查版本
2. **版本比较**: 比较本地版本与API返回的最新版本
3. **用户提示**: 根据比较结果显示相应提示
4. **下载更新**: 提供下载链接供用户更新
5. **强制更新**: 主版本更新时要求强制更新

## 📚 相关文档

- [API使用指南](./API_USAGE_GUIDE.md)
- [项目组织文档](./PROJECT_ORGANIZATION.md)
- [功能特性文档](./docs/FEATURES.md)

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本检查功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .btn-success {
            background-color: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
            background-color: #ecf0f1;
        }
        .result.success {
            border-left-color: #27ae60;
            background-color: #d5f4e6;
        }
        .result.error {
            border-left-color: #e74c3c;
            background-color: #fadbd8;
        }
        .result.warning {
            border-left-color: #f39c12;
            background-color: #fef9e7;
        }
        .version-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        .version-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .test-list {
            list-style: none;
            padding: 0;
        }
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        .test-item.pass {
            background-color: #d5f4e6;
            border-left: 4px solid #27ae60;
        }
        .test-item.fail {
            background-color: #fadbd8;
            border-left: 4px solid #e74c3c;
        }
        .test-icon {
            margin-right: 10px;
            font-weight: bold;
        }
        .test-icon.pass {
            color: #27ae60;
        }
        .test-icon.fail {
            color: #e74c3c;
        }
        .description {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 版本检查功能测试</h1>
        
        <div class="test-section">
            <h2>📋 测试说明</h2>
            <div class="description">
                此页面用于测试HOUT工具箱的版本检查功能。测试包括：
                <ul>
                    <li>版本比较算法测试</li>
                    <li>API调用模拟测试</li>
                    <li>更新逻辑验证</li>
                    <li>UI组件功能测试</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🧮 版本比较算法测试</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="runVersionComparisonTests()">运行版本比较测试</button>
                <button class="btn-secondary" onclick="clearResults('versionComparisonResult')">清除结果</button>
            </div>
            <div id="versionComparisonResult"></div>
        </div>

        <div class="test-section">
            <h2>🌐 API调用模拟测试</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="simulateApiCall()">模拟API调用</button>
                <button class="btn-secondary" onclick="simulateApiError()">模拟API错误</button>
                <button class="btn-secondary" onclick="clearResults('apiTestResult')">清除结果</button>
            </div>
            <div id="apiTestResult"></div>
        </div>

        <div class="test-section">
            <h2>🔄 更新逻辑验证</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="testUpdateLogic()">测试更新逻辑</button>
                <button class="btn-secondary" onclick="clearResults('updateLogicResult')">清除结果</button>
            </div>
            <div id="updateLogicResult"></div>
        </div>

        <div class="test-section">
            <h2>🎯 集成测试</h2>
            <div class="button-group">
                <button class="btn-success" onclick="runAllTests()">运行所有测试</button>
                <button class="btn-primary" onclick="openVersionTestPage()">打开版本测试页面</button>
                <button class="btn-secondary" onclick="clearAllResults()">清除所有结果</button>
            </div>
            <div id="integrationTestResult"></div>
        </div>
    </div>

    <script>
        // 版本比较函数（模拟实际实现）
        function parseVersion(version) {
            const cleanVersion = version.split('-')[0].split('+')[0];
            return cleanVersion.split('.').map(part => {
                const num = parseInt(part, 10);
                return isNaN(num) ? 0 : num;
            });
        }

        function compareVersions(version1, version2) {
            const v1Parts = parseVersion(version1);
            const v2Parts = parseVersion(version2);
            
            const maxLength = Math.max(v1Parts.length, v2Parts.length);
            
            for (let i = 0; i < maxLength; i++) {
                const v1Part = v1Parts[i] || 0;
                const v2Part = v2Parts[i] || 0;
                
                if (v1Part > v2Part) return 1;
                if (v1Part < v2Part) return -1;
            }
            
            return 0;
        }

        // 版本比较测试
        function runVersionComparisonTests() {
            const resultDiv = document.getElementById('versionComparisonResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在运行版本比较测试...';

            setTimeout(() => {
                const tests = [
                    { v1: '1.0.0', v2: '1.0.1', expected: -1, description: '1.0.0 < 1.0.1' },
                    { v1: '1.0.1', v2: '1.0.0', expected: 1, description: '1.0.1 > 1.0.0' },
                    { v1: '1.0.0', v2: '1.0.0', expected: 0, description: '1.0.0 = 1.0.0' },
                    { v1: '1.0.0', v2: '2.0.0', expected: -1, description: '1.0.0 < 2.0.0' },
                    { v1: '2.0.0', v2: '1.9.9', expected: 1, description: '2.0.0 > 1.9.9' },
                    { v1: '1.0.0-beta', v2: '1.0.0', expected: 0, description: '1.0.0-beta = 1.0.0 (忽略预发布标识)' },
                    { v1: '1.0.0+build.1', v2: '1.0.0', expected: 0, description: '1.0.0+build.1 = 1.0.0 (忽略构建元数据)' },
                ];

                let passCount = 0;
                let failCount = 0;
                let html = '<ul class="test-list">';

                tests.forEach(test => {
                    const actual = compareVersions(test.v1, test.v2);
                    const success = actual === test.expected;
                    
                    if (success) passCount++;
                    else failCount++;

                    html += `
                        <li class="test-item ${success ? 'pass' : 'fail'}">
                            <span class="test-icon ${success ? 'pass' : 'fail'}">${success ? '✓' : '✗'}</span>
                            <div>
                                <strong>${test.description}</strong><br>
                                <small>实际: ${actual}, 期望: ${test.expected}</small>
                            </div>
                        </li>
                    `;
                });

                html += '</ul>';
                html += `<div style="margin-top: 15px;"><strong>测试结果: ${passCount} 通过, ${failCount} 失败</strong></div>`;

                resultDiv.innerHTML = html;
                resultDiv.className = `result ${failCount === 0 ? 'success' : 'error'}`;
            }, 1000);
        }

        // 模拟API调用
        function simulateApiCall() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在模拟API调用...';

            setTimeout(() => {
                const mockResponse = {
                    success: true,
                    data: {
                        software: {
                            id: 1,
                            name: "HOUT - 澎湃解锁工具箱",
                            currentVersion: "1.2.0"
                        },
                        versions: [{
                            id: 1,
                            version: "1.2.0",
                            releaseNotes: "修复了一些bug，增加了新功能",
                            releaseDate: "2025-01-29T12:00:00.000Z",
                            isStable: true,
                            versionType: "release"
                        }]
                    }
                };

                const currentVersion = "1.0.0";
                const latestVersion = mockResponse.data.versions[0].version;
                const needsUpdate = compareVersions(currentVersion, latestVersion) < 0;

                let html = `
                    <div class="version-info">
                        <div class="version-row">
                            <strong>当前版本:</strong>
                            <span>${currentVersion}</span>
                        </div>
                        <div class="version-row">
                            <strong>最新版本:</strong>
                            <span>${latestVersion}</span>
                        </div>
                        <div class="version-row">
                            <strong>需要更新:</strong>
                            <span>${needsUpdate ? '是' : '否'}</span>
                        </div>
                        <div class="version-row">
                            <strong>更新说明:</strong>
                            <span>${mockResponse.data.versions[0].releaseNotes}</span>
                        </div>
                    </div>
                `;

                resultDiv.innerHTML = html;
                resultDiv.className = `result ${needsUpdate ? 'warning' : 'success'}`;
            }, 1500);
        }

        // 模拟API错误
        function simulateApiError() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在模拟API错误...';

            setTimeout(() => {
                resultDiv.innerHTML = '<strong>API调用失败:</strong> 网络连接超时，请检查网络设置或稍后重试。';
                resultDiv.className = 'result error';
            }, 1000);
        }

        // 测试更新逻辑
        function testUpdateLogic() {
            const resultDiv = document.getElementById('updateLogicResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在测试更新逻辑...';

            setTimeout(() => {
                const scenarios = [
                    { current: '1.0.0', latest: '1.0.1', expected: '可选更新' },
                    { current: '1.0.0', latest: '2.0.0', expected: '强制更新' },
                    { current: '1.0.0', latest: '1.0.0', expected: '无需更新' },
                    { current: '1.1.0', latest: '1.0.0', expected: '开发版本' },
                ];

                let html = '<ul class="test-list">';
                scenarios.forEach(scenario => {
                    const comparison = compareVersions(scenario.current, scenario.latest);
                    let actualResult;
                    
                    if (comparison < 0) {
                        const currentMajor = parseVersion(scenario.current)[0];
                        const latestMajor = parseVersion(scenario.latest)[0];
                        actualResult = latestMajor > currentMajor ? '强制更新' : '可选更新';
                    } else if (comparison === 0) {
                        actualResult = '无需更新';
                    } else {
                        actualResult = '开发版本';
                    }

                    const success = actualResult === scenario.expected;
                    html += `
                        <li class="test-item ${success ? 'pass' : 'fail'}">
                            <span class="test-icon ${success ? 'pass' : 'fail'}">${success ? '✓' : '✗'}</span>
                            <div>
                                <strong>${scenario.current} → ${scenario.latest}</strong><br>
                                <small>实际: ${actualResult}, 期望: ${scenario.expected}</small>
                            </div>
                        </li>
                    `;
                });
                html += '</ul>';

                resultDiv.innerHTML = html;
                resultDiv.className = 'result success';
            }, 1200);
        }

        // 运行所有测试
        function runAllTests() {
            const resultDiv = document.getElementById('integrationTestResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在运行集成测试...';

            setTimeout(() => {
                runVersionComparisonTests();
                setTimeout(() => {
                    simulateApiCall();
                    setTimeout(() => {
                        testUpdateLogic();
                        setTimeout(() => {
                            resultDiv.innerHTML = '<strong>✅ 所有测试已完成！</strong><br>请查看各个测试部分的详细结果。';
                            resultDiv.className = 'result success';
                        }, 500);
                    }, 1500);
                }, 1500);
            }, 500);
        }

        // 打开版本测试页面
        function openVersionTestPage() {
            const url = window.location.origin + '?test=version';
            window.open(url, '_blank');
        }

        // 清除结果
        function clearResults(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '';
            element.className = '';
        }

        // 清除所有结果
        function clearAllResults() {
            ['versionComparisonResult', 'apiTestResult', 'updateLogicResult', 'integrationTestResult'].forEach(clearResults);
        }
    </script>
</body>
</html>

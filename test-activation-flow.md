# 激活流程测试文档

## 测试目标
验证HOUT工具箱的激活码验证功能，确保：
1. 首次启动时显示欢迎界面
2. 激活码格式验证正确
3. 激活成功后进入主应用
4. 激活码过期时重新显示激活界面

## 测试步骤

### 1. 首次启动测试
- [ ] 启动应用
- [ ] 确认显示欢迎页面
- [ ] 确认显示"欢迎使用 HOUT 工具箱"标题
- [ ] 点击"开始激活"按钮进入激活页面

### 2. 激活码格式验证测试
- [ ] 输入无效格式激活码（如：`123456`）
- [ ] 确认显示格式错误提示
- [ ] 输入有效格式激活码（如：`1K2L3M4N-ABC123-DEF45678`）
- [ ] 确认显示格式正确提示

### 3. 激活成功测试
- [ ] 使用示例激活码：`1K2L3M4N-ABC123-DEF45678`
- [ ] 点击"激活"按钮
- [ ] 确认显示"正在验证激活码"状态
- [ ] 确认激活成功后跳转到完成页面
- [ ] 点击"进入应用"进入主界面

### 4. 激活状态持久化测试
- [ ] 关闭应用
- [ ] 重新启动应用
- [ ] 确认直接进入主界面（不显示欢迎页面）

### 5. 激活码过期测试
- [ ] 手动修改本地存储中的过期时间为过去时间
- [ ] 重新启动应用
- [ ] 确认显示欢迎页面并提示"激活码已过期"
- [ ] 确认激活页面显示"重新激活应用"标题

## 示例激活码
根据新的API格式，以下是可用的测试激活码：
- `1K2L3M4N-ABC123-DEF45678` (演示版)
- `2M3N4O5P-XYZ789-GHI12345` (试用版)
- `3O5P6Q7R-RST456-JKL67890` (完整版)

## 预期行为

### 激活状态检查逻辑
1. **未激活状态**：显示欢迎页面
2. **已激活且未过期**：直接进入主应用
3. **已激活但已过期**：显示欢迎页面，提示重新激活

### 激活码格式要求
- 格式：`{timestamp}-{random}-{uuid}`
- 示例：`1K2L3M4N-ABC123-DEF45678`
- 第一部分：6-10位字母数字（时间戳）
- 第二部分：6位字母数字（随机字符串）
- 第三部分：8位字母数字（UUID片段）

## 错误处理
- 网络错误：显示网络连接错误提示
- 激活码无效：显示激活码无效提示
- 激活码已使用：显示激活码已被使用提示
- 激活码已过期：显示激活码已过期提示

## 测试结果记录
- [ ] 所有测试用例通过
- [ ] 发现的问题：
- [ ] 需要改进的地方：

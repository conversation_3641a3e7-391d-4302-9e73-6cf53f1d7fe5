# 📡 LACS API Server 使用指南

> 🚀 完整的API集成指南，从入门到精通

## 🎯 快速导航

| 章节 | 内容 | 适用场景 |
|------|------|----------|
| [🚀 快速开始](#-快速开始) | 5分钟上手指南 | 新手入门 |
| [🔐 认证方式](#-认证方式) | API Key 和 JWT 认证 | 安全集成 |
| [🔑 激活码管理](#-激活码管理api) | 激活码相关API | 许可证管理 |
| [📦 软件管理](#-软件管理api) | 软件信息管理API | 软件发布 |
| [📝 版本管理](#-版本管理api) | 版本历史管理API | 版本控制 |
| [📢 公告管理](#-公告管理api) | 公告发布管理API | 用户通知 |
| [🚨 错误处理](#-错误处理) | 状态码和错误处理 | 异常处理 |
| [💻 代码示例](#-代码示例) | 多语言集成示例 | 实际开发 |
| [🎯 使用场景](#-常见使用场景) | 最佳实践指南 | 业务集成 |

## 🚀 快速开始

### 📋 基础信息

```bash
# 基础URL
Production:  https://your-domain.com
Development: http://localhost:3000

# 认证方式
API Key:     X-API-Key: your-api-key
JWT Token:   Authorization: Bearer your-jwt-token

# 数据格式
Content-Type: application/json
Accept: application/json
```

### ⚡ 5分钟快速测试

```bash
# 1. 健康检查
curl https://your-domain.com/api/health

# 2. 生成激活码
curl -X POST "https://your-domain.com/api/activation-codes" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"expirationDays": 30}'

# 3. 获取软件列表
curl "https://your-domain.com/app/software" \
  -H "X-API-Key: your-api-key"
```

## 🔐 认证方式

### 🔑 API Key 认证（推荐）

**适用场景**：服务端到服务端的API调用

```javascript
// JavaScript 示例
const response = await fetch('/api/activation-codes', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    expirationDays: 365
  })
});
```

```python
# Python 示例
import requests

headers = {
    'X-API-Key': 'your-api-key',
    'Content-Type': 'application/json'
}

response = requests.post(
    'https://your-domain.com/api/activation-codes',
    headers=headers,
    json={'expirationDays': 365}
)
```

### 🎫 JWT 认证

**适用场景**：管理界面和用户会话

```javascript
// 获取JWT Token
const authResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'password'
  })
});

const { token } = await authResponse.json();

// 使用JWT Token
const response = await fetch('/admin/endpoint', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

## 🔑 激活码管理API

### 📊 API端点总览

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/activation-codes` | 生成激活码 | API Key |
| POST | `/api/activation-codes/verify` | 验证激活码 | API Key |
| GET | `/api/activation-codes` | 查询激活码列表 | API Key |
| GET | `/api/activation-codes/stats` | 获取统计信息 | API Key |
| POST | `/api/activation-codes/cleanup` | 清理过期激活码 | API Key |
| POST | `/api/activation-codes/cleanup-unused` | 清理未使用激活码 | API Key |

### 🎲 生成激活码

**端点**：`POST /api/activation-codes`

**请求参数**：
```json
{
  "expirationDays": 365,          // 过期天数（必需）
  "metadata": {                   // 元数据（可选）
    "purpose": "license",
    "features": ["feature1", "feature2"],
    "userId": "user123"
  },
  "productInfo": {                // 产品信息（可选）
    "name": "软件名称",
    "version": "1.0.0",
    "edition": "professional"
  }
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "code": "MDMNBPJX-3S0P6E-B1360C10",
    "id": 123,
    "createdAt": "2025-01-29T10:00:00.000Z",
    "expiresAt": "2026-01-29T10:00:00.000Z",
    "isActivated": false,
    "metadata": {
      "purpose": "license",
      "features": ["feature1", "feature2"]
    }
  },
  "message": "激活码生成成功"
}
```

### ✅ 验证激活码

**端点**：`POST /api/activation-codes/verify`

**请求参数**：
```json
{
  "code": "MDMNBPJX-3S0P6E-B1360C10"  // 激活码（必需）
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "code": "MDMNBPJX-3S0P6E-B1360C10",
    "isValid": true,
    "isActivated": true,
    "activatedAt": "2025-01-29T10:05:00.000Z",
    "expiresAt": "2026-01-29T10:00:00.000Z",
    "metadata": {
      "purpose": "license",
      "features": ["feature1", "feature2"]
    },
    "productInfo": {
      "name": "软件名称",
      "version": "1.0.0"
    }
  },
  "message": "激活码验证成功"
}
```

### 📋 查询激活码列表

**端点**：`GET /api/activation-codes`

**查询参数**：
```bash
?page=1              # 页码（默认：1）
&limit=10            # 每页数量（默认：10，最大：100）
&status=all          # 状态筛选：all|active|expired|used
&search=MDMNBPJX     # 搜索关键词
&sortBy=createdAt    # 排序字段：createdAt|expiresAt|activatedAt
&sortOrder=desc      # 排序方向：asc|desc
```

**响应示例**：
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "code": "MDMNBPJX-3S0P6E-B1360C10",
      "createdAt": "2025-01-29T10:00:00.000Z",
      "expiresAt": "2026-01-29T10:00:00.000Z",
      "isActivated": true,
      "activatedAt": "2025-01-29T10:05:00.000Z",
      "metadata": {
        "purpose": "license"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### 📊 获取统计信息

**端点**：`GET /api/activation-codes/stats`

**响应示例**：
```json
{
  "success": true,
  "data": {
    "total": 1000,
    "active": 750,
    "expired": 200,
    "used": 600,
    "unused": 400,
    "recentlyCreated": 50,
    "recentlyActivated": 30,
    "expiringThisWeek": 25,
    "activationRate": 60.0,
    "dailyStats": [
      {
        "date": "2025-01-29",
        "created": 10,
        "activated": 8
      }
    ]
  }
}
```

## 📦 软件管理API

### 📊 API端点总览

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/app/software` | 获取软件列表 | API Key |
| POST | `/app/software` | 添加新软件 | API Key |
| GET | `/app/software/id/{id}` | 获取软件详情 | API Key |
| GET | `/app/software/{name}` | 根据名称获取软件 | API Key |
| PUT | `/app/software/id/{id}` | 更新软件信息 | API Key |
| DELETE | `/app/software/id/{id}` | 删除软件 | API Key |

### 📋 获取软件列表

**端点**：`GET /app/software`

**查询参数**：
```bash
?page=1              # 页码
&limit=10            # 每页数量
&category=tools      # 分类筛选
&search=软件名       # 搜索关键词
&sortBy=name         # 排序字段
&sortOrder=asc       # 排序方向
```

**响应示例**：
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "示例软件",
      "nameEn": "Example Software",
      "description": "这是一个示例软件",
      "descriptionEn": "This is an example software",
      "currentVersion": "1.0.0",
      "category": "tools",
      "tags": ["utility", "productivity"],
      "officialWebsite": "https://example.com",
      "createdAt": "2025-01-29T10:00:00.000Z",
      "updatedAt": "2025-01-29T10:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "totalPages": 5
  }
}
```

### ➕ 添加新软件

**端点**：`POST /app/software`

**请求参数**：
```json
{
  "name": "新软件",                    // 软件名称（必需）
  "nameEn": "New Software",           // 英文名称（可选）
  "description": "软件描述",           // 描述（必需）
  "descriptionEn": "Software description", // 英文描述（可选）
  "currentVersion": "1.0.0",          // 当前版本（必需）
  "category": "tools",                // 分类（可选）
  "tags": ["utility", "productivity"], // 标签（可选）
  "officialWebsite": "https://example.com", // 官网（可选）
  "metadata": {                       // 元数据（可选）
    "developer": "开发者名称",
    "license": "MIT",
    "platform": ["Windows", "macOS", "Linux"]
  }
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "新软件",
    "nameEn": "New Software",
    "description": "软件描述",
    "currentVersion": "1.0.0",
    "category": "tools",
    "createdAt": "2025-01-29T10:00:00.000Z"
  },
  "message": "软件添加成功"
}
```

### 🔍 获取软件详情

**端点**：`GET /app/software/id/{id}` 或 `GET /app/software/{name}`

**响应示例**：
```json
{
  "success": true,
  "data": {
    "software": {
      "id": 1,
      "name": "示例软件",
      "nameEn": "Example Software",
      "description": "详细的软件描述",
      "currentVersion": "1.2.0",
      "category": "tools",
      "tags": ["utility", "productivity"],
      "officialWebsite": "https://example.com",
      "metadata": {
        "developer": "开发者名称",
        "license": "MIT"
      },
      "createdAt": "2025-01-29T10:00:00.000Z",
      "updatedAt": "2025-01-29T12:00:00.000Z"
    },
    "versions": [
      {
        "id": 1,
        "version": "1.2.0",
        "releaseDate": "2025-01-29T12:00:00.000Z",
        "isStable": true
      }
    ],
    "announcements": [
      {
        "id": 1,
        "title": "版本更新通知",
        "type": "update",
        "priority": "normal",
        "publishedAt": "2025-01-29T12:00:00.000Z"
      }
    ]
  }
}
```

## 📝 版本管理API

### 📊 API端点总览

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/app/software/id/{id}/versions` | 获取版本历史 | API Key |
| POST | `/app/software/id/{id}/versions` | 添加新版本 | API Key |
| GET | `/app/software/id/{id}/versions/{versionId}` | 获取版本详情 | API Key |
| PUT | `/app/software/id/{id}/versions/{versionId}` | 更新版本信息 | API Key |
| DELETE | `/app/software/id/{id}/versions/{versionId}` | 删除版本 | API Key |

### 📋 获取版本历史

**端点**：`GET /app/software/id/{id}/versions`

**查询参数**：
```bash
?page=1              # 页码
&limit=10            # 每页数量
&versionType=all     # 版本类型：all|release|beta|alpha
&isStable=true       # 是否稳定版
&sortBy=releaseDate  # 排序字段
&sortOrder=desc      # 排序方向
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "software": {
      "id": 1,
      "name": "示例软件",
      "currentVersion": "1.2.0"
    },
    "versions": [
      {
        "id": 1,
        "version": "1.2.0",
        "releaseNotes": "修复了一些bug，增加了新功能",
        "releaseNotesEn": "Fixed bugs and added new features",
        "releaseDate": "2025-01-29T12:00:00.000Z",
        "downloadLinks": {
          "official": "https://download.com/v1.2.0.zip",
          "quark": "https://pan.quark.cn/file",
          "baidu": "https://pan.baidu.com/file"
        },
        "fileSize": "150MB",
        "isStable": true,
        "versionType": "release",
        "metadata": {
          "buildNumber": "1200",
          "commitHash": "abc123"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### ➕ 添加新版本

**端点**：`POST /app/software/id/{id}/versions`

**请求参数**：
```json
{
  "version": "1.3.0",                 // 版本号（必需）
  "releaseNotes": "更新内容",         // 更新说明（必需）
  "releaseNotesEn": "Release notes",  // 英文更新说明（可选）
  "releaseDate": "2025-01-30T10:00:00.000Z", // 发布时间（可选，默认当前时间）
  "downloadLinks": {                  // 下载链接（可选）
    "official": "https://download.com/v1.3.0.zip",
    "quark": "https://pan.quark.cn/file",
    "baidu": "https://pan.baidu.com/file",
    "github": "https://github.com/user/repo/releases/tag/v1.3.0"
  },
  "fileSize": "160MB",                // 文件大小（可选）
  "isStable": true,                   // 是否稳定版（默认：true）
  "versionType": "release",           // 版本类型（默认：release）
  "metadata": {                       // 元数据（可选）
    "buildNumber": "1300",
    "commitHash": "def456",
    "changelog": ["新增功能A", "修复bug B"]
  }
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "id": 2,
    "version": "1.3.0",
    "releaseNotes": "更新内容",
    "releaseDate": "2025-01-30T10:00:00.000Z",
    "downloadLinks": {
      "official": "https://download.com/v1.3.0.zip"
    },
    "isStable": true,
    "versionType": "release"
  },
  "message": "版本添加成功"
}
```

## 📢 公告管理API

### 📊 API端点总览

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/app/software/id/{id}/announcements` | 获取公告列表 | API Key |
| POST | `/app/software/id/{id}/announcements` | 添加新公告 | API Key |
| GET | `/app/software/id/{id}/announcements/{announcementId}` | 获取公告详情 | API Key |
| PUT | `/app/software/id/{id}/announcements/{announcementId}` | 更新公告 | API Key |
| DELETE | `/app/software/id/{id}/announcements/{announcementId}` | 删除公告 | API Key |

### 📋 获取公告列表

**端点**：`GET /app/software/id/{id}/announcements`

**查询参数**：
```bash
?page=1              # 页码
&limit=10            # 每页数量
&type=all            # 公告类型：all|general|update|security|maintenance
&priority=all        # 优先级：all|low|normal|high|urgent
&isPublished=true    # 是否已发布
&sortBy=publishedAt  # 排序字段
&sortOrder=desc      # 排序方向
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "software": {
      "id": 1,
      "name": "示例软件"
    },
    "announcements": [
      {
        "id": 1,
        "title": "重要更新通知",
        "titleEn": "Important Update Notice",
        "content": "我们发布了新版本，包含重要的安全修复...",
        "contentEn": "We have released a new version with important security fixes...",
        "type": "update",
        "priority": "high",
        "version": "1.3.0",
        "isPublished": true,
        "publishedAt": "2025-01-29T10:00:00.000Z",
        "expiresAt": "2025-02-28T23:59:59.000Z",
        "metadata": {
          "author": "管理员",
          "tags": ["security", "update"]
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "totalPages": 2
    }
  }
}
```

### ➕ 添加新公告

**端点**：`POST /app/software/id/{id}/announcements`

**请求参数**：
```json
{
  "title": "公告标题",               // 标题（必需）
  "titleEn": "Announcement Title", // 英文标题（可选）
  "content": "公告内容详情...",      // 内容（必需）
  "contentEn": "Announcement content...", // 英文内容（可选）
  "type": "update",                // 类型（默认：general）
  "priority": "high",              // 优先级（默认：normal）
  "version": "1.3.0",              // 关联版本（可选）
  "isPublished": true,             // 是否发布（默认：true）
  "publishedAt": "2025-01-29T10:00:00.000Z", // 发布时间（可选）
  "expiresAt": "2025-02-28T23:59:59.000Z",   // 过期时间（可选）
  "metadata": {                    // 元数据（可选）
    "author": "管理员",
    "tags": ["important", "update"],
    "targetAudience": "all"
  }
}
```

**公告类型说明**：
- `general`: 一般公告
- `update`: 更新通知
- `security`: 安全公告
- `maintenance`: 维护通知
- `feature`: 功能介绍
- `bugfix`: 修复通知

**优先级说明**：
- `low`: 低优先级
- `normal`: 普通优先级
- `high`: 高优先级
- `urgent`: 紧急优先级

**响应示例**：
```json
{
  "success": true,
  "data": {
    "id": 2,
    "title": "公告标题",
    "content": "公告内容详情...",
    "type": "update",
    "priority": "high",
    "isPublished": true,
    "publishedAt": "2025-01-29T10:00:00.000Z"
  },
  "message": "公告添加成功"
}
```

## 🚨 错误处理

### 📊 HTTP状态码

| 状态码 | 含义 | 描述 | 处理建议 |
|--------|------|------|----------|
| `200` | ✅ OK | 请求成功 | 正常处理响应数据 |
| `201` | ✅ Created | 资源创建成功 | 资源已成功创建 |
| `400` | ❌ Bad Request | 请求参数错误 | 检查请求参数格式和必填字段 |
| `401` | ❌ Unauthorized | 认证失败 | 检查API Key或JWT Token |
| `403` | ❌ Forbidden | 权限不足 | 检查用户权限或API访问范围 |
| `404` | ❌ Not Found | 资源不存在 | 检查资源ID或URL路径 |
| `409` | ❌ Conflict | 资源冲突 | 检查是否存在重复资源 |
| `422` | ❌ Unprocessable Entity | 数据验证失败 | 检查数据格式和业务规则 |
| `429` | ⚠️ Too Many Requests | 请求频率超限 | 降低请求频率，实施退避策略 |
| `500` | ❌ Internal Server Error | 服务器内部错误 | 联系技术支持或稍后重试 |

### 🔍 错误响应格式

**标准错误响应**：
```json
{
  "success": false,
  "error": "错误描述信息",
  "code": "ERROR_CODE",
  "details": {
    "field": "具体错误字段",
    "message": "详细错误信息"
  },
  "timestamp": "2025-01-29T10:00:00.000Z",
  "path": "/api/activation-codes"
}
```
```

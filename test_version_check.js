/**
 * 版本检查功能测试脚本
 * 用于验证版本比较算法和更新逻辑
 */

// 版本比较函数（模拟TypeScript实现）
function parseVersion(version) {
    const cleanVersion = version.split('-')[0].split('+')[0];
    return cleanVersion.split('.').map(part => {
        const num = parseInt(part, 10);
        return isNaN(num) ? 0 : num;
    });
}

function compareVersions(version1, version2) {
    const v1Parts = parseVersion(version1);
    const v2Parts = parseVersion(version2);
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
        const v1Part = v1Parts[i] || 0;
        const v2Part = v2Parts[i] || 0;
        
        if (v1Part > v2Part) return 1;
        if (v1Part < v2Part) return -1;
    }
    
    return 0;
}

function isGreater(version1, version2) {
    return compareVersions(version1, version2) > 0;
}

function isLess(version1, version2) {
    return compareVersions(version1, version2) < 0;
}

function isEqual(version1, version2) {
    return compareVersions(version1, version2) === 0;
}

// 测试用例
const testCases = [
    // 基本版本比较
    { v1: '1.0.0', v2: '1.0.1', expected: -1, description: '1.0.0 < 1.0.1' },
    { v1: '1.0.1', v2: '1.0.0', expected: 1, description: '1.0.1 > 1.0.0' },
    { v1: '1.0.0', v2: '1.0.0', expected: 0, description: '1.0.0 = 1.0.0' },
    
    // 主版本号比较
    { v1: '1.0.0', v2: '2.0.0', expected: -1, description: '1.0.0 < 2.0.0' },
    { v1: '2.0.0', v2: '1.9.9', expected: 1, description: '2.0.0 > 1.9.9' },
    
    // 次版本号比较
    { v1: '1.1.0', v2: '1.2.0', expected: -1, description: '1.1.0 < 1.2.0' },
    { v1: '1.2.0', v2: '1.1.9', expected: 1, description: '1.2.0 > 1.1.9' },
    
    // 修订版本号比较
    { v1: '1.0.1', v2: '1.0.2', expected: -1, description: '1.0.1 < 1.0.2' },
    { v1: '1.0.2', v2: '1.0.1', expected: 1, description: '1.0.2 > 1.0.1' },
    
    // 预发布版本（应该忽略）
    { v1: '1.0.0-beta', v2: '1.0.0', expected: 0, description: '1.0.0-beta = 1.0.0 (忽略预发布标识)' },
    { v1: '1.0.0-alpha', v2: '1.0.0-beta', expected: 0, description: '1.0.0-alpha = 1.0.0-beta (忽略预发布标识)' },
    
    // 构建元数据（应该忽略）
    { v1: '1.0.0+build.1', v2: '1.0.0', expected: 0, description: '1.0.0+build.1 = 1.0.0 (忽略构建元数据)' },
    { v1: '1.0.0+build.1', v2: '1.0.0+build.2', expected: 0, description: '1.0.0+build.1 = 1.0.0+build.2 (忽略构建元数据)' },
    
    // 不同长度的版本号
    { v1: '1.0', v2: '1.0.0', expected: 0, description: '1.0 = 1.0.0' },
    { v1: '1.0.0', v2: '1.0', expected: 0, description: '1.0.0 = 1.0' },
    { v1: '1.0', v2: '1.0.1', expected: -1, description: '1.0 < 1.0.1' },
    
    // 边界情况
    { v1: '0.0.1', v2: '0.0.2', expected: -1, description: '0.0.1 < 0.0.2' },
    { v1: '10.0.0', v2: '9.9.9', expected: 1, description: '10.0.0 > 9.9.9' },
];

// 更新逻辑测试用例
const updateLogicTests = [
    { current: '1.0.0', latest: '1.0.1', expectedType: '可选更新', description: '小版本更新' },
    { current: '1.0.0', latest: '1.1.0', expectedType: '可选更新', description: '次版本更新' },
    { current: '1.0.0', latest: '2.0.0', expectedType: '强制更新', description: '主版本更新' },
    { current: '1.0.0', latest: '1.0.0', expectedType: '无需更新', description: '版本相同' },
    { current: '1.1.0', latest: '1.0.0', expectedType: '开发版本', description: '当前版本更高' },
    { current: '2.0.0-beta', latest: '1.9.9', expectedType: '开发版本', description: '预发布版本' },
];

// 运行版本比较测试
function runVersionComparisonTests() {
    console.log('🧮 开始运行版本比较测试...\n');
    
    let passCount = 0;
    let failCount = 0;
    
    testCases.forEach((test, index) => {
        const actual = compareVersions(test.v1, test.v2);
        const success = actual === test.expected;
        
        if (success) {
            passCount++;
            console.log(`✅ 测试 ${index + 1}: ${test.description}`);
        } else {
            failCount++;
            console.log(`❌ 测试 ${index + 1}: ${test.description}`);
            console.log(`   期望: ${test.expected}, 实际: ${actual}`);
        }
    });
    
    console.log(`\n📊 版本比较测试结果: ${passCount} 通过, ${failCount} 失败\n`);
    return { passCount, failCount };
}

// 运行更新逻辑测试
function runUpdateLogicTests() {
    console.log('🔄 开始运行更新逻辑测试...\n');
    
    let passCount = 0;
    let failCount = 0;
    
    updateLogicTests.forEach((test, index) => {
        const comparison = compareVersions(test.current, test.latest);
        let actualType;
        
        if (comparison < 0) {
            // 当前版本低于最新版本
            const currentMajor = parseVersion(test.current)[0];
            const latestMajor = parseVersion(test.latest)[0];
            actualType = latestMajor > currentMajor ? '强制更新' : '可选更新';
        } else if (comparison === 0) {
            actualType = '无需更新';
        } else {
            actualType = '开发版本';
        }
        
        const success = actualType === test.expectedType;
        
        if (success) {
            passCount++;
            console.log(`✅ 测试 ${index + 1}: ${test.description} (${test.current} → ${test.latest})`);
        } else {
            failCount++;
            console.log(`❌ 测试 ${index + 1}: ${test.description} (${test.current} → ${test.latest})`);
            console.log(`   期望: ${test.expectedType}, 实际: ${actualType}`);
        }
    });
    
    console.log(`\n📊 更新逻辑测试结果: ${passCount} 通过, ${failCount} 失败\n`);
    return { passCount, failCount };
}

// 模拟API响应测试
function simulateApiResponse() {
    console.log('🌐 模拟API响应测试...\n');
    
    const mockResponse = {
        success: true,
        data: {
            software: {
                id: 1,
                name: "HOUT - 澎湃解锁工具箱",
                currentVersion: "1.2.0",
                description: "Android设备管理工具"
            },
            versions: [
                {
                    id: 1,
                    version: "1.2.0",
                    releaseNotes: "修复了一些bug，增加了新功能",
                    releaseDate: "2025-01-29T12:00:00.000Z",
                    downloadLinks: {
                        official: "https://download.com/v1.2.0.zip",
                        github: "https://github.com/user/repo/releases/tag/v1.2.0"
                    },
                    fileSize: "150MB",
                    isStable: true,
                    versionType: "release"
                }
            ]
        }
    };
    
    console.log('📦 模拟API响应数据:');
    console.log(JSON.stringify(mockResponse, null, 2));
    
    // 模拟版本检查逻辑
    const currentVersion = "1.0.0";
    const latestVersion = mockResponse.data.versions[0].version;
    const needsUpdate = isLess(currentVersion, latestVersion);
    const isForceUpdate = parseVersion(currentVersion)[0] < parseVersion(latestVersion)[0];
    
    console.log('\n🔍 版本检查结果:');
    console.log(`当前版本: ${currentVersion}`);
    console.log(`最新版本: ${latestVersion}`);
    console.log(`需要更新: ${needsUpdate ? '是' : '否'}`);
    console.log(`强制更新: ${isForceUpdate ? '是' : '否'}`);
    console.log(`更新说明: ${mockResponse.data.versions[0].releaseNotes}`);
    console.log(`文件大小: ${mockResponse.data.versions[0].fileSize}\n`);
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行版本检查功能完整测试...\n');
    console.log('=' .repeat(60));
    
    const versionResults = runVersionComparisonTests();
    console.log('=' .repeat(60));
    
    const updateResults = runUpdateLogicTests();
    console.log('=' .repeat(60));
    
    simulateApiResponse();
    console.log('=' .repeat(60));
    
    const totalPass = versionResults.passCount + updateResults.passCount;
    const totalFail = versionResults.failCount + updateResults.failCount;
    const totalTests = totalPass + totalFail;
    
    console.log('📈 总体测试结果:');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${totalPass}`);
    console.log(`失败: ${totalFail}`);
    console.log(`成功率: ${((totalPass / totalTests) * 100).toFixed(1)}%`);
    
    if (totalFail === 0) {
        console.log('\n🎉 所有测试通过！版本检查功能实现正确。');
    } else {
        console.log('\n⚠️  部分测试失败，请检查实现逻辑。');
    }
    
    console.log('\n📚 测试完成！可以继续进行集成测试。');
}

// 如果是在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        compareVersions,
        isGreater,
        isLess,
        isEqual,
        runVersionComparisonTests,
        runUpdateLogicTests,
        simulateApiResponse,
        runAllTests
    };
}

// 如果是在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.versionTestUtils = {
        compareVersions,
        isGreater,
        isLess,
        isEqual,
        runVersionComparisonTests,
        runUpdateLogicTests,
        simulateApiResponse,
        runAllTests
    };
}

// 自动运行测试（如果直接执行此脚本）
if (typeof require !== 'undefined' && require.main === module) {
    runAllTests();
} else {
    // 确保在任何环境下都能运行测试
    runAllTests();
}

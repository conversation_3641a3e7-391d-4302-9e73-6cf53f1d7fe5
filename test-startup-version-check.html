<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动版本检查功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .btn-success {
            background-color: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .btn-warning {
            background-color: #f39c12;
            color: white;
        }
        .btn-warning:hover {
            background-color: #e67e22;
        }
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c0392b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
            background-color: #ecf0f1;
        }
        .result.success {
            border-left-color: #27ae60;
            background-color: #d5f4e6;
        }
        .result.error {
            border-left-color: #e74c3c;
            background-color: #fadbd8;
        }
        .result.warning {
            border-left-color: #f39c12;
            background-color: #fef9e7;
        }
        .scenario {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        .scenario-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            text-align: center;
        }
        .scenario-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .scenario-card p {
            margin: 5px 0;
            font-size: 14px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .description {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
            margin-right: 8px;
        }
        .mock-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            max-width: 500px;
            width: 90%;
            z-index: 1000;
            display: none;
        }
        .mock-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }
        .version-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .version-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 启动版本检查功能测试</h1>
        
        <div class="test-section">
            <h2>📋 功能说明</h2>
            <div class="description">
                启动版本检查是Tauri跨平台应用的核心功能，确保用户始终使用最新版本。
                <br><br>
                <strong>主要特性：</strong>
            </div>
            <ul class="feature-list">
                <li>应用启动时自动检查版本</li>
                <li>使用软件ID为1查询最新版本信息</li>
                <li>从Tauri配置获取本地版本号</li>
                <li>语义化版本号比较</li>
                <li>强制更新弹窗（无法跳过）</li>
                <li>最新版本提示（自动消失）</li>
                <li>网络异常降级处理</li>
                <li>10秒超时保护</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🎭 模拟测试场景</h2>
            <div class="button-group">
                <button class="btn-success" onclick="simulateLatestVersion()">模拟最新版本</button>
                <button class="btn-warning" onclick="simulateNeedsUpdate()">模拟需要更新</button>
                <button class="btn-danger" onclick="simulateNetworkError()">模拟网络错误</button>
                <button class="btn-secondary" onclick="simulateTimeout()">模拟超时</button>
            </div>
            
            <div class="scenario">
                <div class="scenario-card">
                    <h4>✅ 最新版本</h4>
                    <p><strong>当前:</strong> 1.0.0</p>
                    <p><strong>最新:</strong> 1.0.0</p>
                    <p><strong>结果:</strong> 显示成功提示</p>
                </div>
                <div class="scenario-card">
                    <h4>⚠️ 需要更新</h4>
                    <p><strong>当前:</strong> 1.0.0</p>
                    <p><strong>最新:</strong> 1.2.0</p>
                    <p><strong>结果:</strong> 强制更新弹窗</p>
                </div>
                <div class="scenario-card">
                    <h4>❌ 网络错误</h4>
                    <p><strong>状态:</strong> 连接失败</p>
                    <p><strong>选项:</strong> 重试或离线使用</p>
                    <p><strong>结果:</strong> 错误处理对话框</p>
                </div>
            </div>
            
            <div id="simulationResult"></div>
        </div>

        <div class="test-section">
            <h2>🔧 实际功能测试</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="openStartupTest()">打开启动测试页面</button>
                <button class="btn-primary" onclick="openVersionTest()">打开版本测试页面</button>
                <button class="btn-secondary" onclick="testVersionComparison()">测试版本比较</button>
                <button class="btn-secondary" onclick="testTauriIntegration()">测试Tauri集成</button>
            </div>
            <div id="functionalTestResult"></div>
        </div>

        <div class="test-section">
            <h2>📊 性能测试</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="testStartupTime()">测试启动时间</button>
                <button class="btn-secondary" onclick="testTimeoutHandling()">测试超时处理</button>
                <button class="btn-secondary" onclick="testRetryMechanism()">测试重试机制</button>
            </div>
            <div id="performanceTestResult"></div>
        </div>
    </div>

    <!-- 模拟对话框 -->
    <div class="mock-overlay" id="mockOverlay" onclick="closeMockDialog()"></div>
    <div class="mock-dialog" id="mockDialog">
        <h3 id="mockDialogTitle">标题</h3>
        <div id="mockDialogContent">内容</div>
        <div style="text-align: right; margin-top: 20px;">
            <button class="btn-secondary" onclick="closeMockDialog()">关闭</button>
        </div>
    </div>

    <script>
        // 模拟最新版本场景
        function simulateLatestVersion() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在检查版本...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ 当前是最新版本</h4>
                        <div class="version-info">
                            <div class="version-row">
                                <span><strong>当前版本:</strong></span>
                                <span>1.0.0</span>
                            </div>
                            <div class="version-row">
                                <span><strong>最新版本:</strong></span>
                                <span>1.0.0</span>
                            </div>
                            <div class="version-row">
                                <span><strong>检查结果:</strong></span>
                                <span style="color: #27ae60;">无需更新</span>
                            </div>
                        </div>
                        <p><strong>用户体验:</strong> 显示3秒成功提示后自动进入主应用</p>
                    </div>
                `;
            }, 1500);
        }

        // 模拟需要更新场景
        function simulateNeedsUpdate() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在检查版本...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="result warning">
                        <h4>⚠️ 发现新版本</h4>
                        <div class="version-info">
                            <div class="version-row">
                                <span><strong>当前版本:</strong></span>
                                <span>1.0.0</span>
                            </div>
                            <div class="version-row">
                                <span><strong>最新版本:</strong></span>
                                <span>1.2.0</span>
                            </div>
                            <div class="version-row">
                                <span><strong>更新类型:</strong></span>
                                <span style="color: #e74c3c;">强制更新</span>
                            </div>
                        </div>
                        <p><strong>用户体验:</strong> 显示强制更新对话框，用户无法跳过</p>
                        <button class="btn-warning" onclick="showMockUpdateDialog()">查看更新对话框</button>
                    </div>
                `;
            }, 1500);
        }

        // 模拟网络错误场景
        function simulateNetworkError() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在检查版本...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 网络连接失败</h4>
                        <p><strong>错误信息:</strong> 无法连接到更新服务器</p>
                        <p><strong>用户选项:</strong></p>
                        <ul>
                            <li>重试版本检查</li>
                            <li>继续离线使用</li>
                        </ul>
                        <button class="btn-danger" onclick="showMockErrorDialog()">查看错误对话框</button>
                    </div>
                `;
            }, 3000);
        }

        // 模拟超时场景
        function simulateTimeout() {
            const resultDiv = document.getElementById('simulationResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在检查版本...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>⏰ 版本检查超时</h4>
                        <p><strong>超时时间:</strong> 10秒</p>
                        <p><strong>处理方式:</strong> 自动显示错误对话框，提供重试选项</p>
                        <p><strong>用户体验:</strong> 不会无限等待，确保应用能够正常启动</p>
                    </div>
                `;
            }, 10000);
        }

        // 显示模拟更新对话框
        function showMockUpdateDialog() {
            document.getElementById('mockDialogTitle').textContent = '发现新版本';
            document.getElementById('mockDialogContent').innerHTML = `
                <div style="text-align: center; margin: 20px 0;">
                    <div style="font-size: 48px; color: #f39c12; margin-bottom: 15px;">⚠️</div>
                    <h3>需要更新到最新版本</h3>
                    <p style="color: #7f8c8d;">检测到新版本，请更新后继续使用</p>
                </div>
                <div class="version-info">
                    <div class="version-row">
                        <span><strong>当前版本:</strong></span>
                        <span>1.0.0</span>
                    </div>
                    <div class="version-row">
                        <span><strong>最新版本:</strong></span>
                        <span>1.2.0</span>
                    </div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <strong>更新说明:</strong><br>
                    • 修复了重要的安全漏洞<br>
                    • 优化了应用性能<br>
                    • 新增了多项实用功能<br>
                    <small style="color: #7f8c8d;">文件大小: 150MB</small>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-primary" onclick="alert('正在打开下载链接...')">立即更新</button>
                </div>
            `;
            showMockDialog();
        }

        // 显示模拟错误对话框
        function showMockErrorDialog() {
            document.getElementById('mockDialogTitle').textContent = '版本检查失败';
            document.getElementById('mockDialogContent').innerHTML = `
                <div style="color: #e74c3c; margin: 20px 0;">
                    <strong>⚠️ 无法连接到更新服务器</strong>
                </div>
                <p>无法连接到更新服务器，您可以选择重试或继续离线使用。</p>
                <div style="display: flex; gap: 10px; justify-content: space-between; margin-top: 20px;">
                    <button class="btn-secondary" onclick="alert('选择离线使用')">离线使用</button>
                    <button class="btn-primary" onclick="alert('正在重试...')">重试</button>
                </div>
            `;
            showMockDialog();
        }

        // 显示模拟对话框
        function showMockDialog() {
            document.getElementById('mockOverlay').style.display = 'block';
            document.getElementById('mockDialog').style.display = 'block';
        }

        // 关闭模拟对话框
        function closeMockDialog() {
            document.getElementById('mockOverlay').style.display = 'none';
            document.getElementById('mockDialog').style.display = 'none';
        }

        // 打开启动测试页面
        function openStartupTest() {
            const url = window.location.origin.replace(':3000', ':1420') + '?startup=true';
            window.open(url, '_blank');
        }

        // 打开版本测试页面
        function openVersionTest() {
            const url = window.location.origin.replace(':3000', ':1420') + '?test=version';
            window.open(url, '_blank');
        }

        // 测试版本比较
        function testVersionComparison() {
            const resultDiv = document.getElementById('functionalTestResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在测试版本比较算法...';
            
            setTimeout(() => {
                const tests = [
                    { current: '1.0.0', latest: '1.0.1', expected: '需要更新' },
                    { current: '1.0.0', latest: '1.0.0', expected: '最新版本' },
                    { current: '1.1.0', latest: '1.0.0', expected: '最新版本' },
                ];
                
                let html = '<div class="result success"><h4>版本比较测试结果</h4>';
                tests.forEach(test => {
                    html += `<p>当前: ${test.current}, 最新: ${test.latest} → ${test.expected}</p>`;
                });
                html += '</div>';
                
                resultDiv.innerHTML = html;
            }, 1000);
        }

        // 测试Tauri集成
        function testTauriIntegration() {
            const resultDiv = document.getElementById('functionalTestResult');
            resultDiv.innerHTML = `
                <div class="result">
                    <h4>Tauri集成测试</h4>
                    <p><strong>版本获取:</strong> 使用 @tauri-apps/api/app 的 getVersion() 方法</p>
                    <p><strong>配置文件:</strong> src-tauri/tauri.conf.json</p>
                    <p><strong>当前版本:</strong> 1.0.0</p>
                    <p><strong>环境检测:</strong> ${typeof window !== 'undefined' ? '浏览器环境' : 'Node.js环境'}</p>
                </div>
            `;
        }

        // 测试启动时间
        function testStartupTime() {
            const resultDiv = document.getElementById('performanceTestResult');
            const startTime = Date.now();
            resultDiv.innerHTML = '<div class="loading"></div>正在测试启动时间...';
            
            setTimeout(() => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                resultDiv.innerHTML = `
                    <div class="result ${duration < 3000 ? 'success' : 'warning'}">
                        <h4>启动时间测试</h4>
                        <p><strong>版本检查耗时:</strong> ${duration}ms</p>
                        <p><strong>性能评级:</strong> ${duration < 2000 ? '优秀' : duration < 3000 ? '良好' : '需要优化'}</p>
                        <p><strong>目标时间:</strong> < 3000ms</p>
                    </div>
                `;
            }, Math.random() * 2000 + 1000);
        }

        // 测试超时处理
        function testTimeoutHandling() {
            const resultDiv = document.getElementById('performanceTestResult');
            resultDiv.innerHTML = '<div class="loading"></div>正在测试超时处理...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>超时处理测试</h4>
                        <p><strong>超时设置:</strong> 10秒</p>
                        <p><strong>处理方式:</strong> 自动显示错误对话框</p>
                        <p><strong>用户体验:</strong> 不会无限等待</p>
                        <p><strong>测试结果:</strong> ✅ 超时机制正常工作</p>
                    </div>
                `;
            }, 1500);
        }

        // 测试重试机制
        function testRetryMechanism() {
            const resultDiv = document.getElementById('performanceTestResult');
            let retryCount = 0;
            
            function performRetry() {
                retryCount++;
                resultDiv.innerHTML = `<div class="loading"></div>正在重试... (第${retryCount}次)`;
                
                setTimeout(() => {
                    if (retryCount < 3) {
                        performRetry();
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h4>重试机制测试</h4>
                                <p><strong>重试次数:</strong> ${retryCount}</p>
                                <p><strong>重试间隔:</strong> 1-2秒</p>
                                <p><strong>用户控制:</strong> 可选择停止重试</p>
                                <p><strong>测试结果:</strong> ✅ 重试机制正常工作</p>
                            </div>
                        `;
                    }
                }, 1000 + Math.random() * 1000);
            }
            
            performRetry();
        }
    </script>
</body>
</html>

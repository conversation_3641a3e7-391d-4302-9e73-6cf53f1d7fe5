<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全防护测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px dashed #ccc;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section:hover {
            border-color: #007acc;
            background: #e6f3ff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.enabled {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disabled {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-result.pass {
            background: #d4edda;
            color: #155724;
        }
        .test-result.fail {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ 安全防护功能测试</h1>
        
        <div class="warning">
            <strong>⚠️ 注意：</strong>这是一个独立的测试页面，用于验证安全防护功能。请在主应用中启用安全防护后再进行测试。
        </div>

        <div id="status" class="status disabled">
            安全防护状态：未知
        </div>

        <h2>测试项目</h2>

        <div class="test-section">
            <h3>1. 右键菜单测试</h3>
            <p>在此区域右键点击，如果防护正常，右键菜单应该被禁用。</p>
            <div id="contextmenu-test" style="padding: 20px; background: #e9ecef; text-align: center;">
                右键点击这里测试
            </div>
            <div id="contextmenu-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 键盘快捷键测试</h3>
            <p>尝试以下快捷键组合：</p>
            <ul>
                <li><strong>F12</strong> - 开发者工具</li>
                <li><strong>Ctrl+Shift+I</strong> - 开发者工具</li>
                <li><strong>Ctrl+Shift+J</strong> - 控制台</li>
                <li><strong>Ctrl+U</strong> - 查看源代码</li>
            </ul>
            <button onclick="testKeyboardShortcuts()">开始键盘测试</button>
            <div id="keyboard-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 文本选择测试</h3>
            <p>尝试选择下面的文本，如果防护正常，文本应该无法被选择。</p>
            <div id="selection-test" style="padding: 20px; background: #e9ecef;">
                这段文字应该无法被选择。如果你能选择这段文字，说明文本选择防护没有生效。
            </div>
            <div id="selection-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 开发者工具检测测试</h3>
            <p>打开开发者工具（F12），系统应该能检测到并显示警告。</p>
            <button onclick="testDevToolsDetection()">检测开发者工具状态</button>
            <div id="devtools-result" class="test-result" style="display: none;"></div>
        </div>

        <h2>测试结果</h2>
        <div id="overall-result" class="test-result" style="display: none;"></div>
    </div>

    <script>
        // 模拟安全防护功能（简化版本）
        let protectionEnabled = false;
        let testResults = {};

        // 检查是否在主应用中
        function checkMainApp() {
            // 检查是否有安全防护相关的元素或变量
            if (window.parent !== window || 
                document.querySelector('[data-security-enabled]') ||
                window.securityProtection) {
                protectionEnabled = true;
                updateStatus('enabled', '安全防护状态：已启用');
            } else {
                updateStatus('disabled', '安全防护状态：未启用（独立测试页面）');
            }
        }

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function showResult(elementId, success, message) {
            const resultEl = document.getElementById(elementId);
            resultEl.className = `test-result ${success ? 'pass' : 'fail'}`;
            resultEl.textContent = message;
            resultEl.style.display = 'block';
            
            testResults[elementId] = success;
            updateOverallResult();
        }

        function updateOverallResult() {
            const results = Object.values(testResults);
            if (results.length === 0) return;
            
            const passed = results.filter(r => r).length;
            const total = results.length;
            const success = passed === total;
            
            const overallEl = document.getElementById('overall-result');
            overallEl.className = `test-result ${success ? 'pass' : 'fail'}`;
            overallEl.textContent = `总体测试结果：${passed}/${total} 项通过 ${success ? '✅' : '❌'}`;
            overallEl.style.display = 'block';
        }

        // 右键菜单测试
        document.getElementById('contextmenu-test').addEventListener('contextmenu', function(e) {
            if (protectionEnabled) {
                e.preventDefault();
                showResult('contextmenu-result', true, '✅ 右键菜单已被成功阻止');
            } else {
                showResult('contextmenu-result', false, '❌ 右键菜单未被阻止');
            }
        });

        // 键盘快捷键测试
        function testKeyboardShortcuts() {
            let blocked = 0;
            const shortcuts = ['F12', 'Ctrl+Shift+I', 'Ctrl+Shift+J', 'Ctrl+U'];
            
            document.addEventListener('keydown', function(e) {
                if (e.key === 'F12' || 
                    (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
                    (e.ctrlKey && e.key === 'U')) {
                    if (protectionEnabled) {
                        e.preventDefault();
                        blocked++;
                    }
                }
            });

            setTimeout(() => {
                const success = protectionEnabled && blocked > 0;
                showResult('keyboard-result', success, 
                    success ? '✅ 键盘快捷键防护正常' : '❌ 键盘快捷键未被阻止');
            }, 2000);
        }

        // 文本选择测试
        document.getElementById('selection-test').addEventListener('selectstart', function(e) {
            if (protectionEnabled) {
                e.preventDefault();
                showResult('selection-result', true, '✅ 文本选择已被成功阻止');
            } else {
                showResult('selection-result', false, '❌ 文本选择未被阻止');
            }
        });

        // 开发者工具检测测试
        function testDevToolsDetection() {
            // 简单的开发者工具检测
            let devtoolsOpen = false;
            
            // 检测窗口尺寸变化
            const threshold = 160;
            if (window.outerHeight - window.innerHeight > threshold || 
                window.outerWidth - window.innerWidth > threshold) {
                devtoolsOpen = true;
            }

            const success = protectionEnabled && devtoolsOpen;
            showResult('devtools-result', success, 
                devtoolsOpen ? 
                    (protectionEnabled ? '✅ 开发者工具已检测到，防护已激活' : '⚠️ 开发者工具已检测到，但防护未启用') :
                    '❌ 未检测到开发者工具');
        }

        // 初始化
        checkMainApp();
        
        // 定期检查状态
        setInterval(checkMainApp, 1000);
    </script>
</body>
</html>

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# ESLint cache
.eslintcache

# Tauri specific
src-tauri/target/
src-tauri/gen/
src-tauri/Cargo.lock

# Backup files
*.backup
*.bak
*.ab

# Package manager files
pnpm-debug.log*
lerna-debug.log*

# Cache directories
.cache/
.parcel-cache/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional npm cache directory
.npm

# Yarn Integrity file
.yarn-integrity

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Test coverage
*.lcov
.nyc_output/

# Build artifacts
out/

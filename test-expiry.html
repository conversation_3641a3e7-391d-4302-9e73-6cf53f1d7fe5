<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码过期测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #0066cc;
        }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0052a3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        .success {
            background: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 激活码过期测试工具</h1>
        
        <div class="section">
            <h3>📋 当前激活状态</h3>
            <div id="currentStatus" class="status">正在检查...</div>
            <button onclick="checkCurrentStatus()">刷新状态</button>
        </div>

        <div class="section">
            <h3>⚠️ 模拟激活码过期</h3>
            <div class="warning">
                <strong>注意：</strong>此操作会修改本地存储的激活信息，使激活码显示为已过期状态。
            </div>
            <button class="danger" onclick="simulateExpiry()">模拟激活码过期</button>
            <button onclick="setExpiryDate(1)">设置1天后过期</button>
            <button onclick="setExpiryDate(7)">设置7天后过期</button>
        </div>

        <div class="section">
            <h3>🔄 重置激活状态</h3>
            <div class="info">
                重置所有激活信息，回到初始未激活状态。
            </div>
            <button class="danger" onclick="resetActivation()">完全重置激活状态</button>
        </div>

        <div class="section">
            <h3>✅ 设置有效激活</h3>
            <div class="info">
                快速设置一个有效的激活状态用于测试。
            </div>
            <button onclick="setValidActivation()">设置有效激活（30天）</button>
            <button onclick="setValidActivation(365)">设置有效激活（1年）</button>
        </div>

        <div class="section">
            <h3>📊 本地存储数据</h3>
            <pre id="storageData">正在加载...</pre>
            <button onclick="showStorageData()">刷新数据</button>
        </div>
    </div>

    <script>
        // 检查当前激活状态
        function checkCurrentStatus() {
            const statusDiv = document.getElementById('currentStatus');
            try {
                const configData = localStorage.getItem('hout-app-config');
                if (!configData) {
                    statusDiv.innerHTML = '<span style="color: #6c757d;">未找到激活配置</span>';
                    statusDiv.className = 'status';
                    return;
                }

                const config = JSON.parse(configData);
                const appConfig = config.state?.config;
                
                if (!appConfig) {
                    statusDiv.innerHTML = '<span style="color: #6c757d;">配置数据格式错误</span>';
                    statusDiv.className = 'status';
                    return;
                }

                const isActivated = appConfig.isActivated;
                const expiryDate = appConfig.expiryDate ? new Date(appConfig.expiryDate) : null;
                const now = new Date();
                const isExpired = expiryDate && now > expiryDate;

                let statusText = '';
                let statusClass = 'status';

                if (!isActivated) {
                    statusText = '❌ 未激活';
                    statusClass = 'status';
                } else if (isExpired) {
                    statusText = `⚠️ 已激活但已过期 (过期时间: ${expiryDate.toLocaleString('zh-CN')})`;
                    statusClass = 'status warning';
                } else {
                    statusText = `✅ 已激活且有效 ${expiryDate ? `(过期时间: ${expiryDate.toLocaleString('zh-CN')})` : '(无过期时间)'}`;
                    statusClass = 'status success';
                }

                statusDiv.innerHTML = statusText;
                statusDiv.className = statusClass;
            } catch (error) {
                statusDiv.innerHTML = `<span style="color: #dc3545;">错误: ${error.message}</span>`;
                statusDiv.className = 'status';
            }
        }

        // 模拟激活码过期
        function simulateExpiry() {
            if (!confirm('确定要模拟激活码过期吗？这会修改本地存储数据。')) {
                return;
            }

            try {
                const configData = localStorage.getItem('hout-app-config');
                if (!configData) {
                    alert('未找到激活配置，请先进行激活');
                    return;
                }

                const config = JSON.parse(configData);
                if (config.state?.config) {
                    // 设置过期时间为昨天
                    const yesterday = new Date();
                    yesterday.setDate(yesterday.getDate() - 1);
                    
                    config.state.config.expiryDate = yesterday.toISOString();
                    config.state.config.isActivated = true; // 保持激活状态，但设置为过期
                    
                    localStorage.setItem('hout-app-config', JSON.stringify(config));
                    alert('✅ 已模拟激活码过期，请重新启动应用查看效果');
                    checkCurrentStatus();
                    showStorageData();
                } else {
                    alert('配置数据格式错误');
                }
            } catch (error) {
                alert(`操作失败: ${error.message}`);
            }
        }

        // 设置过期日期
        function setExpiryDate(days) {
            try {
                const configData = localStorage.getItem('hout-app-config');
                if (!configData) {
                    alert('未找到激活配置，请先进行激活');
                    return;
                }

                const config = JSON.parse(configData);
                if (config.state?.config) {
                    const futureDate = new Date();
                    futureDate.setDate(futureDate.getDate() + days);
                    
                    config.state.config.expiryDate = futureDate.toISOString();
                    config.state.config.isActivated = true;
                    config.state.config.activationStatus = 'activated';
                    
                    localStorage.setItem('hout-app-config', JSON.stringify(config));
                    alert(`✅ 已设置激活码${days}天后过期`);
                    checkCurrentStatus();
                    showStorageData();
                } else {
                    alert('配置数据格式错误');
                }
            } catch (error) {
                alert(`操作失败: ${error.message}`);
            }
        }

        // 重置激活状态
        function resetActivation() {
            if (!confirm('确定要完全重置激活状态吗？这会清除所有激活信息。')) {
                return;
            }

            localStorage.removeItem('hout-app-config');
            alert('✅ 已重置激活状态，请重新启动应用');
            checkCurrentStatus();
            showStorageData();
        }

        // 设置有效激活
        function setValidActivation(days = 30) {
            try {
                const futureDate = new Date();
                futureDate.setDate(futureDate.getDate() + days);
                
                const config = {
                    state: {
                        config: {
                            isActivated: true,
                            activationStatus: 'activated',
                            userConfig: {
                                username: 'HOUT用户',
                                language: 'zh-CN',
                                theme: 'light',
                                autoStart: false,
                                checkUpdates: true,
                                enableTelemetry: false
                            },
                            activationDate: new Date().toISOString(),
                            expiryDate: futureDate.toISOString(),
                            features: ['device_management', 'screen_mirror', 'file_transfer'],
                            version: '1.0.0'
                        }
                    },
                    version: 1
                };
                
                localStorage.setItem('hout-app-config', JSON.stringify(config));
                alert(`✅ 已设置有效激活状态（${days}天有效期）`);
                checkCurrentStatus();
                showStorageData();
            } catch (error) {
                alert(`操作失败: ${error.message}`);
            }
        }

        // 显示存储数据
        function showStorageData() {
            const dataDiv = document.getElementById('storageData');
            try {
                const configData = localStorage.getItem('hout-app-config');
                if (!configData) {
                    dataDiv.textContent = '无激活配置数据';
                    return;
                }

                const config = JSON.parse(configData);
                dataDiv.textContent = JSON.stringify(config, null, 2);
            } catch (error) {
                dataDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            checkCurrentStatus();
            showStorageData();
        };
    </script>
</body>
</html>

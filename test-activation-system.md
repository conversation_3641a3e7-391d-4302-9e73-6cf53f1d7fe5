# 激活验证系统测试文档

## 测试概述

本文档描述了HOUT工具箱激活验证系统的完整测试流程，包括各种场景的测试用例和预期结果。

## 测试环境

- **操作系统**: Windows 10/11
- **Node.js**: 18+
- **Rust**: 1.77+
- **Tauri**: 2.0+

## 功能测试

### 1. 激活码格式验证测试

#### 测试用例 1.1: 有效激活码格式
```
输入: 1K2L3M4N-ABC123-DEF45678
预期结果: 格式验证通过
```

#### 测试用例 1.2: 无效激活码格式
```
输入: INVALID-CODE
预期结果: 格式验证失败，显示错误提示
```

#### 测试用例 1.3: 空激活码
```
输入: (空字符串)
预期结果: 显示"激活码不能为空"错误
```

#### 测试用例 1.4: 过长激活码
```
输入: 1K2L3M4N-ABC123-DEF45678-EXTRA-LONG-PART
预期结果: 格式验证失败
```

#### 测试用例 1.5: 包含特殊字符
```
输入: 1K2L3M4N-ABC123-DEF45678<script>
预期结果: 安全验证失败，拒绝输入
```

### 2. 网络验证测试

#### 测试用例 2.1: 有效激活码验证
```
前提: 使用有效的激活码
步骤:
1. 输入有效激活码
2. 点击激活按钮
3. 等待网络验证
预期结果: 激活成功，显示成功消息和功能列表
```

#### 测试用例 2.2: 已使用激活码
```
前提: 使用已被使用的激活码
预期结果: 显示"激活码已被使用"错误
```

#### 测试用例 2.3: 过期激活码
```
前提: 使用已过期的激活码
预期结果: 显示"激活码已过期"错误
```

#### 测试用例 2.4: 网络连接失败
```
前提: 断开网络连接
预期结果: 显示网络连接错误提示
```

### 3. 本地存储测试

#### 测试用例 3.1: 激活数据保存
```
步骤:
1. 成功激活应用
2. 重启应用
预期结果: 应用直接进入主界面，不显示激活页面
```

#### 测试用例 3.2: 激活数据加密
```
步骤:
1. 成功激活应用
2. 检查localStorage中的数据
预期结果: 激活数据已加密存储，无法直接读取
```

#### 测试用例 3.3: 数据完整性验证
```
步骤:
1. 成功激活应用
2. 手动修改localStorage中的激活数据
3. 重启应用
预期结果: 检测到数据被篡改，要求重新激活
```

### 4. 过期处理测试

#### 测试用例 4.1: 过期提醒
```
前提: 激活码剩余7天过期
预期结果: 显示过期提醒消息条
```

#### 测试用例 4.2: 过期对话框
```
前提: 激活码已过期
预期结果: 显示过期对话框，提供重新激活选项
```

#### 测试用例 4.3: 重新激活流程
```
步骤:
1. 激活码过期
2. 点击"立即重新激活"
3. 输入新的激活码
预期结果: 成功重新激活，恢复所有功能
```

### 5. 用户界面测试

#### 测试用例 5.1: 标签页切换
```
步骤:
1. 在激活页面切换不同标签页
2. 检查内容显示
预期结果: 标签页正常切换，内容正确显示
```

#### 测试用例 5.2: 激活状态显示
```
前提: 激活成功
预期结果: 激活状态卡片显示正确的状态信息
```

#### 测试用例 5.3: 帮助信息显示
```
步骤:
1. 切换到帮助标签页
2. 查看帮助内容
预期结果: 显示完整的帮助信息和FAQ
```

## 安全测试

### 1. 输入验证测试

#### 测试用例 S1: SQL注入防护
```
输入: 1K2L3M4N'; DROP TABLE users; --
预期结果: 输入被拒绝，显示安全错误
```

#### 测试用例 S2: XSS防护
```
输入: <script>alert('xss')</script>
预期结果: 输入被拒绝，显示安全错误
```

#### 测试用例 S3: 长度限制
```
输入: 超过50字符的激活码
预期结果: 输入被拒绝，显示长度错误
```

### 2. 数据保护测试

#### 测试用例 S4: 加密存储
```
验证: localStorage中的激活数据是否加密
预期结果: 数据已加密，无法直接读取
```

#### 测试用例 S5: 数据完整性
```
验证: 激活数据是否包含校验和
预期结果: 包含校验和，可检测数据篡改
```

## 性能测试

### 1. 响应时间测试

#### 测试用例 P1: 格式验证响应时间
```
测试: 输入激活码后的格式验证时间
预期结果: 小于100ms
```

#### 测试用例 P2: 网络验证响应时间
```
测试: 网络激活验证时间
预期结果: 正常网络下小于5秒
```

### 2. 内存使用测试

#### 测试用例 P3: 内存占用
```
测试: 激活过程中的内存使用
预期结果: 内存增长合理，无内存泄漏
```

## 兼容性测试

### 1. 浏览器兼容性

#### 测试用例 C1: 不同浏览器内核
```
测试环境: Chromium, WebKit
预期结果: 功能正常，界面一致
```

### 2. 操作系统兼容性

#### 测试用例 C2: Windows版本
```
测试环境: Windows 10, Windows 11
预期结果: 功能正常，性能稳定
```

## 错误处理测试

### 1. 网络错误

#### 测试用例 E1: 网络超时
```
模拟: 网络请求超时
预期结果: 显示超时错误，提供重试选项
```

#### 测试用例 E2: 服务器错误
```
模拟: 服务器返回500错误
预期结果: 显示服务器错误，建议稍后重试
```

### 2. 数据错误

#### 测试用例 E3: 数据损坏
```
模拟: 本地存储数据损坏
预期结果: 清除损坏数据，要求重新激活
```

## 测试执行步骤

### 1. 准备测试环境
```bash
# 克隆项目
git clone <repository-url>
cd hout-tauri

# 安装依赖
npm install

# 启动开发服务器
npm run tauri dev
```

### 2. 执行功能测试
1. 按照测试用例逐一执行
2. 记录测试结果
3. 截图保存关键步骤

### 3. 执行安全测试
1. 尝试各种恶意输入
2. 验证安全防护机制
3. 检查数据保护措施

### 4. 执行性能测试
1. 使用开发者工具监控性能
2. 记录响应时间和资源使用
3. 分析性能瓶颈

## 测试报告模板

### 测试结果记录
```
测试用例ID: [用例编号]
测试描述: [测试内容]
执行时间: [执行日期时间]
测试结果: [通过/失败]
实际结果: [实际观察到的结果]
问题描述: [如果失败，描述问题]
截图: [相关截图文件名]
```

### 问题跟踪
```
问题ID: [问题编号]
问题描述: [详细描述]
严重程度: [高/中/低]
复现步骤: [详细步骤]
预期结果: [应该的结果]
实际结果: [实际的结果]
状态: [待修复/已修复/已验证]
```

## 验收标准

### 功能验收
- [ ] 所有格式验证测试通过
- [ ] 所有网络验证测试通过
- [ ] 所有本地存储测试通过
- [ ] 所有过期处理测试通过
- [ ] 所有用户界面测试通过

### 安全验收
- [ ] 所有输入验证测试通过
- [ ] 所有数据保护测试通过
- [ ] 无安全漏洞

### 性能验收
- [ ] 响应时间符合要求
- [ ] 内存使用合理
- [ ] 无性能问题

### 兼容性验收
- [ ] 支持的操作系统正常运行
- [ ] 界面显示正常

## 注意事项

1. **测试数据**: 使用专门的测试激活码，避免使用生产环境数据
2. **网络环境**: 在不同网络环境下测试，包括慢速网络
3. **数据备份**: 测试前备份重要数据，避免数据丢失
4. **日志记录**: 开启详细日志，便于问题排查
5. **版本控制**: 记录测试的代码版本，确保测试结果可追溯

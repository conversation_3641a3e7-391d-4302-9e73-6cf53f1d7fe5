# HOUT - 澎湃解锁工具箱 (Tauri版本)

一个现代化的Android设备管理工具，采用Tauri框架重构，提供接近原生Windows应用的使用体验。

## ✨ 功能特色

### 📱 设备管理
- **实时设备检测** - 自动扫描连接的Android设备
- **设备信息展示** - 完整的设备硬件和系统信息
- **多设备支持** - 同时管理多个连接的设备
- **设备状态监控** - 实时显示设备连接状态和模式

### 📁 文件管理
- **文件传输** - 设备与电脑间的文件上传下载
- **APK管理** - 应用安装、卸载、批量操作
- **传输队列** - 可视化的文件传输进度管理
- **安装历史** - 完整的操作记录和管理

### 🏪 APK市场
- **云端APK库** - 集成在线APK资源库
- **分类浏览** - 按类别组织的APK应用
- **智能搜索** - 快速查找所需应用
- **一键下载** - 支持直链和重定向下载
- **自动安装** - 下载完成后自动安装到设备
- **下载管理** - 完整的下载队列和进度管理

### 🔧 ADB工具集成
- **命令执行器** - 自定义ADB命令执行
- **快捷命令** - 预设常用ADB操作
- **实时输出** - 命令执行结果实时显示
- **历史记录** - 命令执行历史和输出保存

### 🎮 设备控制
- **重启控制** - 多种重启模式选择
- **快捷操作** - 截屏、按键模拟、应用启动
- **系统信息** - 实时系统状态监控
- **安全确认** - 危险操作的详细警告

### 🛠️ 专业工具箱
- **Bootloader工具** - 解锁、锁定、OEM信息
- **系统工具** - 数据备份、缓存清理、系统转储
- **开发者工具** - 调试模式、网络测试、压力测试

## 🎨 界面设计

### WinUI 3现代化设计
- **Fluent Design** - 微软最新设计语言
- **自定义标题栏** - 无边框现代窗口
- **响应式布局** - 三栏网格布局优化
- **主题系统** - 完整的亮暗主题支持

## 🚀 技术架构

### 前端技术栈
- **React 18** + **TypeScript** - 现代化前端框架
- **Fluent UI React** - 微软官方WinUI 3风格组件库
- **Zustand** - 轻量级状态管理
- **Vite** - 快速的构建工具

### 后端技术栈
- **Rust** - 高性能系统编程语言
- **Tauri** - 跨平台桌面应用框架
- **Tokio** - 异步运行时
- **Serde** - 序列化/反序列化

## 📂 项目结构

```
hout-tauri/
├── src/                    # 前端源代码
│   ├── components/         # React组件
│   │   ├── TitleBar/      # 自定义标题栏
│   │   ├── MainContent/   # 主内容区域
│   │   ├── DeviceInfo/    # 设备信息面板
│   │   ├── FileManager/   # 文件管理面板
│   │   ├── AppManager/    # APK管理面板
│   │   ├── AdbTools/      # ADB工具面板
│   │   ├── DeviceControl/ # 设备控制面板
│   │   ├── Tools/         # 专业工具箱
│   │   ├── Settings/      # 应用设置
│   │   ├── ScreenMirror/  # 屏幕镜像
│   │   ├── Security/      # 安全保护
│   │   ├── Welcome/       # 欢迎页面
│   │   └── Common/        # 通用组件
│   ├── services/          # 服务层
│   ├── stores/            # 状态管理
│   ├── types/             # TypeScript类型定义
│   ├── styles/            # 全局样式
│   ├── hooks/             # 自定义React Hooks
│   └── utils/             # 工具函数
├── src-tauri/             # Rust后端
│   ├── src/               # Rust源代码
│   ├── icons/             # 应用图标
│   ├── capabilities/      # Tauri权限配置
│   ├── resources/         # 资源文件
│   └── target/            # 构建输出(已忽略)
├── docs/                  # 项目文档
│   ├── FEATURES.md        # 功能说明
│   ├── BUILD.md           # 构建说明
│   ├── SECURITY_PROTECTION.md # 安全保护文档
│   └── ...                # 其他文档
├── tests/                 # 测试文件
│   ├── test_features.md   # 功能测试
│   ├── test_security.html # 安全测试
│   └── demo_security.js   # 安全演示
├── scripts/               # 构建脚本
│   ├── build.ps1          # Windows构建脚本
│   └── build.sh           # Linux/macOS构建脚本
├── tools/                 # 外部工具
│   ├── scrcpy-win32-v3.3.1/ # 屏幕镜像工具
│   └── activation-server/ # 激活服务器
└── dist/                  # 构建输出(已忽略)
```

## 📋 开发环境要求

- **Node.js** 18+
- **Rust** 1.77+
- **Tauri CLI** 2.0+
- **Windows** 10/11
- **Android SDK Platform Tools** (ADB/Fastboot)

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd hout-tauri
```

### 2. 安装依赖
```bash
# 安装前端依赖
npm install

# 安装Tauri CLI (如果未安装)
cargo install tauri-cli
```

### 3. 开发模式运行
```bash
# 启动开发服务器
npm run tauri dev
```

### 4. 构建生产版本
```bash
# 构建前端
npm run build

# 构建Tauri应用
npm run tauri build
```

## 📊 项目状态

### ✅ 已完成功能
- **完整的设备管理系统** - 设备检测、信息展示、状态监控
- **文件传输和APK管理** - 上传下载、安装卸载、批量操作
- **ADB命令集成和执行** - 自定义命令、快捷操作、实时输出
- **设备控制和重启管理** - 多模式重启、快捷操作、安全确认
- **专业工具箱集成** - Bootloader工具、系统工具、开发者工具
- **现代化UI界面** - WinUI 3设计、主题切换、响应式布局
- **应用设置和配置管理** - 完整的设置持久化

### 🎯 项目亮点
1. **完全重构成功** - 从PySide6到Tauri的完整迁移
2. **功能全面增强** - 所有原有功能都得到改进
3. **界面现代化** - 符合最新设计趋势
4. **性能显著提升** - Rust后端带来的性能优势
5. **代码质量优秀** - TypeScript类型安全 + 模块化设计
6. **用户体验优化** - 更直观、更流畅的操作体验

## 📝 使用说明

### 设备连接
1. 启用Android设备的USB调试
2. 连接设备到电脑
3. 授权调试连接
4. 应用会自动检测设备

### 主要功能
- **设备信息** - 查看设备详细信息和实时状态
- **APK市场** - 在线APK资源库，一键下载安装
- **APK管理** - 安装、卸载、管理应用程序
- **文件管理** - 传输文件和管理设备存储
- **ADB工具** - 执行自定义ADB命令
- **设备控制** - 重启设备、快捷操作
- **工具箱** - 专业的设备管理工具

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

MIT License

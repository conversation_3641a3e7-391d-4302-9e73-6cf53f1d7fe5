use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(<PERSON><PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub enum HoutError {
    #[error("ADB command failed: {message}")]
    AdbCommandFailed { message: String },
    
    #[error("Device not found: {serial}")]
    DeviceNotFound { serial: String },
    
    #[error("Device unauthorized: {serial}")]
    DeviceUnauthorized { serial: String },
    
    #[error("File operation failed: {message}")]
    FileOperationFailed { message: String },
    
    #[error("Invalid device mode: {mode}")]
    InvalidDeviceMode { mode: String },
    
    #[error("Command timeout: {command}")]
    CommandTimeout { command: String },
    
    #[error("Parse error: {message}")]
    ParseError { message: String },
    
    #[error("IO error: {message}")]
    IoError { message: String },
    
    #[error("Configuration error: {message}")]
    ConfigError { message: String },
    
    #[error("Unknown error: {message}")]
    Unknown { message: String },

    #[error("File not found: {path}")]
    FileNotFound { path: String },

    #[error("Command failed: {command} - {error}")]
    CommandFailed { command: String, error: String },

    #[error("APK parsing failed: {message}")]
    ApkParsingFailed { message: String },

    #[error("Installation failed: {package} - {reason}")]
    InstallationFailed { package: String, reason: String },

    #[error("Uninstallation failed: {package} - {reason}")]
    UninstallationFailed { package: String, reason: String },

    #[error("Device not ready: {serial} - {state}")]
    DeviceNotReady { serial: String, state: String },

    #[error("ADB not available: {message}")]
    AdbNotAvailable { message: String },

    #[error("Permission denied: {operation}")]
    PermissionDenied { operation: String },

    #[error("Insufficient storage: {required} bytes needed")]
    InsufficientStorage { required: u64 },

    #[error("Network error: {0}")]
    Network(String),

    #[error("IO error: {0}")]
    Io(String),

    #[error("Device error: {0}")]
    Device(String),

    #[error("Process error: {0}")]
    Process(String),

    #[error("Tool error: {0}")]
    Tool(String),

    #[error("Tauri error: {0}")]
    Tauri(String),
}

impl From<std::io::Error> for HoutError {
    fn from(err: std::io::Error) -> Self {
        HoutError::IoError {
            message: err.to_string(),
        }
    }
}

impl From<serde_json::Error> for HoutError {
    fn from(err: serde_json::Error) -> Self {
        HoutError::ParseError {
            message: err.to_string(),
        }
    }
}

impl From<anyhow::Error> for HoutError {
    fn from(err: anyhow::Error) -> Self {
        HoutError::Unknown {
            message: err.to_string(),
        }
    }
}

pub type Result<T> = std::result::Result<T, HoutError>;
